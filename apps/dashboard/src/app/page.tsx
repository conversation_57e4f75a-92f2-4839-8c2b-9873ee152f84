import { redirect } from "next/navigation";
import { AuthDAL } from "@/src/features/auth/dal/auth-dal";

/**
 * Root Page - Smart Redirect Handler
 *
 * Handles the root URL "/" by implementing smart redirect logic based on user authentication
 * and status. This prevents 404 errors and provides seamless navigation to the appropriate
 * destination for each user.
 *
 * Redirect Logic:
 * - Unauthenticated users → /sign-in
 * - Authenticated users → Uses AuthDAL.checkAuthPageAccess() logic:
 *   - Incomplete onboarding → /onboarding/workspace
 *   - Has defaultWorkspace → /{defaultWorkspace}/home
 *   - Has active organization → /{activeOrganization.slug}/home
 *   - Has organizations → /{firstOrganization.slug}/home
 *   - Unverified email → /verify
 *   - Fallback → /sign-in
 */
export default async function RootPage() {
  try {
    // Use the same logic as auth page guard to determine where authenticated users should go
    const authResult = await AuthDAL.checkAuthPageAccess();

    if (authResult.shouldRedirect && authResult.redirectTo) {
      // User is authenticated and should be redirected to their appropriate destination
      redirect(authResult.redirectTo);
    } else {
      // User is not authenticated, redirect to sign-in
      redirect("/sign-in");
    }
  } catch (error) {
    // Check if this is a Next.js redirect (which should not be caught)
    if (
      error &&
      typeof error === "object" &&
      "digest" in error &&
      typeof error.digest === "string" &&
      error.digest.startsWith("NEXT_REDIRECT")
    ) {
      // Re-throw redirect errors so Next.js can handle them properly
      throw error;
    }

    // If there's any other error in the authentication check, safely redirect to sign-in
    console.error("Error in root page redirect logic:", error);
    redirect("/sign-in");
  }
}
